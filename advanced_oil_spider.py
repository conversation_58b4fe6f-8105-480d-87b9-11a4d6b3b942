#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级原油价格爬虫 - 使用Selenium处理动态内容
爬取 https://zh.tradingeconomics.com/commodity/crude-oil 网站的摘要内容和1D数据
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import json
import time
import csv
from datetime import datetime
import re

class AdvancedOilSpider:
    def __init__(self):
        self.urls = {
            'crude-oil': "https://zh.tradingeconomics.com/commodity/crude-oil",
            'brent-crude-oil': "https://zh.tradingeconomics.com/commodity/brent-crude-oil",
            'gasoline': "https://zh.tradingeconomics.com/commodity/gasoline"
        }
        self.driver = None
        self.wait = None
        self.target_commodities = ['原油', '布伦特原油', '汽油']
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, 20)
            
            print("Chrome驱动设置成功")
            return True
        except Exception as e:
            print(f"设置Chrome驱动失败: {e}")
            return False
    
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            print(f"正在访问: {url}")
            self.driver.get(url)

            # 等待页面加载
            time.sleep(5)

            # 等待关键元素加载
            try:
                self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            except:
                print("页面加载超时，继续尝试解析...")

            # 滚动页面以确保所有内容加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)

            return self.driver.page_source
        except Exception as e:
            print(f"获取页面内容失败: {e}")
            return None
    
    def extract_summary(self, html_content, commodity_type):
        """提取摘要内容 - 专门提取市场分析段落"""
        soup = BeautifulSoup(html_content, 'lxml')

        summary_data = {
            'commodity_type': commodity_type,
            'title': '',
            'main_content': '',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 提取标题
        title_selectors = ['h1', '.page-title', '.commodity-title']
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                summary_data['title'] = title_elem.get_text(strip=True)
                break

        # 查找包含市场分析的主要内容
        content_paragraphs = []

        # 方法1: 查找div元素中的内容（目标内容通常在div中）
        divs = soup.find_all('div')
        for div in divs:
            text = div.get_text(strip=True)
            # 查找包含WTI期货分析的div
            if (len(text) > 200 and
                'WTI' in text and '期货' in text and
                any(keyword in text for keyword in ['特朗普', '俄罗斯', '制裁', '欧盟', '贸易协议', '投资者', '谈判']) and
                not any(skip in text for skip in ['版权', '联系', '关于', '隐私', '更多', '指标', '国家', 'Trading Economics'])):
                content_paragraphs.append(text)

        # 方法2: 查找段落中的内容
        if not content_paragraphs:
            paragraphs = soup.find_all('p')
            for p in paragraphs:
                text = p.get_text(strip=True)
                if (len(text) > 200 and
                    any(keyword in text for keyword in ['WTI', '期货', '特朗普', '俄罗斯', '制裁', '欧盟', '贸易协议', '投资者', '谈判']) and
                    not any(skip in text for skip in ['版权', '联系', '关于', '隐私', '更多', '指标', '国家', 'Trading Economics'])):
                    content_paragraphs.append(text)

        # 方法3: 如果还没找到，查找包含基本市场关键词的内容
        if not content_paragraphs:
            all_elements = soup.find_all(['p', 'div'])
            for elem in all_elements:
                text = elem.get_text(strip=True)
                if (len(text) > 100 and
                    any(keyword in text for keyword in ['上涨', '下跌', '美元', '桶', '价格', '市场', '供应', '需求']) and
                    not any(skip in text for skip in ['版权', '联系', '关于', '隐私', '更多', '指标', '国家'])):
                    content_paragraphs.append(text)

        # 取最长的内容作为主要内容
        if content_paragraphs:
            content_paragraphs.sort(key=len, reverse=True)
            summary_data['main_content'] = content_paragraphs[0]

        return summary_data
    
    def extract_commodity_data(self, html_content):
        """提取商品数据"""
        soup = BeautifulSoup(html_content, 'lxml')
        commodities_data = []

        # 目标商品映射
        target_commodities = {
            'crude-oil': '原油',
            'brent-crude-oil': '布伦特原油',
            'gasoline': '汽油'
        }

        # 方法1: 查找商品价格表格（页面中的主要价格表）
        # 寻找包含商品价格的表格或div结构
        price_rows = soup.find_all('tr')
        for row in price_rows:
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 6:  # 至少6列：商品名、价格、变化、变化%、月变化、年变化
                row_data = [cell.get_text(strip=True) for cell in cells]

                # 检查第一列是否包含目标商品
                first_cell = row_data[0].lower()
                for key, name in target_commodities.items():
                    if (key.replace('-', '') in first_cell.replace(' ', '') or
                        name in row_data[0] or
                        ('原油' in row_data[0] and key == 'crude-oil') or
                        ('布伦特' in row_data[0] and key == 'brent-crude-oil') or
                        ('汽油' in row_data[0] and key == 'gasoline')):

                        # 根据调试结果，数据结构是：列1=商品名，列2=价格，列3=空/图片，列4=价格变化，列5=变化%
                        change_price = row_data[3] if len(row_data) > 3 else ''  # 第4列是价格变化值

                        # 判断涨跌方向
                        change_direction = ''
                        if change_price:
                            try:
                                change_val = float(change_price)
                                if change_val > 0:
                                    change_direction = '上升'
                                elif change_val < 0:
                                    change_direction = '下降'
                            except:
                                pass

                        commodity_info = {
                            'commodity': name,
                            'price': row_data[1] if len(row_data) > 1 else '',
                            'change': change_direction,
                            'change_price': change_price,
                            'daily_change': row_data[4] if len(row_data) > 4 else '',  # 第5列是日变化百分比
                            'monthly_change': row_data[5] if len(row_data) > 5 else '',
                            'yearly_change': row_data[6] if len(row_data) > 6 else '',
                            'date': datetime.now().strftime('%Y-%m-%d'),
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        commodities_data.append(commodity_info)
                        break

        # 方法2: 使用Selenium查找特定的商品链接和价格
        if self.driver:
            try:
                # 查找原油相关链接
                oil_links = self.driver.find_elements(By.PARTIAL_LINK_TEXT, "原油")
                oil_links.extend(self.driver.find_elements(By.XPATH, "//a[contains(@href, 'crude-oil')]"))

                for link in oil_links:
                    try:
                        # 获取链接所在行的数据
                        row = link.find_element(By.XPATH, "./ancestor::tr[1]")
                        cells = row.find_elements(By.TAG_NAME, "td")

                        if len(cells) >= 6:
                            # 根据调试结果：列1=商品名，列2=价格，列3=空/图片，列4=价格变化，列5=变化%
                            change_price = cells[3].text.strip() if len(cells) > 3 else ''
                            change_direction = ''

                            # 判断涨跌方向
                            if change_price:
                                try:
                                    change_val = float(change_price)
                                    if change_val > 0:
                                        change_direction = '上升'
                                    elif change_val < 0:
                                        change_direction = '下降'
                                except:
                                    pass

                            # 检查第3列是否有图片来确定涨跌方向（作为备用方法）
                            if not change_direction and len(cells) > 2:
                                img_elements = cells[2].find_elements(By.TAG_NAME, "img")
                                if img_elements:
                                    img_class = img_elements[0].get_attribute("class") or ""
                                    if 'market-negative-image' in img_class:
                                        change_direction = '下降'
                                    elif 'market-positive-image' in img_class:
                                        change_direction = '上升'

                            commodity_info = {
                                'commodity': '原油',
                                'price': cells[1].text.strip() if len(cells) > 1 else '',
                                'change': change_direction,
                                'change_price': change_price,
                                'daily_change': cells[4].text.strip() if len(cells) > 4 else '',
                                'monthly_change': cells[5].text.strip() if len(cells) > 5 else '',
                                'yearly_change': cells[6].text.strip() if len(cells) > 6 else '',
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
                            commodities_data.append(commodity_info)
                            break
                    except:
                        continue

                # 查找布伦特原油
                brent_links = self.driver.find_elements(By.PARTIAL_LINK_TEXT, "布伦特")
                brent_links.extend(self.driver.find_elements(By.XPATH, "//a[contains(@href, 'brent')]"))

                for link in brent_links:
                    try:
                        row = link.find_element(By.XPATH, "./ancestor::tr[1]")
                        cells = row.find_elements(By.TAG_NAME, "td")

                        if len(cells) >= 6:
                            # 根据调试结果：列1=商品名，列2=价格，列3=空/图片，列4=价格变化，列5=变化%
                            change_price = cells[3].text.strip() if len(cells) > 3 else ''
                            change_direction = ''

                            # 判断涨跌方向
                            if change_price:
                                try:
                                    change_val = float(change_price)
                                    if change_val > 0:
                                        change_direction = '上升'
                                    elif change_val < 0:
                                        change_direction = '下降'
                                except:
                                    pass

                            commodity_info = {
                                'commodity': '布伦特原油',
                                'price': cells[1].text.strip() if len(cells) > 1 else '',
                                'change': change_direction,
                                'change_price': change_price,
                                'daily_change': cells[4].text.strip() if len(cells) > 4 else '',
                                'monthly_change': cells[5].text.strip() if len(cells) > 5 else '',
                                'yearly_change': cells[6].text.strip() if len(cells) > 6 else '',
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
                            commodities_data.append(commodity_info)
                            break
                    except:
                        continue

                # 查找汽油
                gasoline_links = self.driver.find_elements(By.PARTIAL_LINK_TEXT, "汽油")
                gasoline_links.extend(self.driver.find_elements(By.XPATH, "//a[contains(@href, 'gasoline')]"))

                for link in gasoline_links:
                    try:
                        row = link.find_element(By.XPATH, "./ancestor::tr[1]")
                        cells = row.find_elements(By.TAG_NAME, "td")

                        if len(cells) >= 6:
                            # 根据调试结果：列1=商品名，列2=价格，列3=空/图片，列4=价格变化，列5=变化%
                            change_price = cells[3].text.strip() if len(cells) > 3 else ''
                            change_direction = ''

                            # 判断涨跌方向
                            if change_price:
                                try:
                                    change_val = float(change_price)
                                    if change_val > 0:
                                        change_direction = '上升'
                                    elif change_val < 0:
                                        change_direction = '下降'
                                except:
                                    pass

                            commodity_info = {
                                'commodity': '汽油',
                                'price': cells[1].text.strip() if len(cells) > 1 else '',
                                'change': change_direction,
                                'change_price': change_price,
                                'daily_change': cells[4].text.strip() if len(cells) > 4 else '',
                                'monthly_change': cells[5].text.strip() if len(cells) > 5 else '',
                                'yearly_change': cells[6].text.strip() if len(cells) > 6 else '',
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
                            commodities_data.append(commodity_info)
                            break
                    except:
                        continue

            except Exception as e:
                print(f"Selenium提取数据时出错: {e}")

        return commodities_data
    
    def save_summaries_to_file(self, summaries_data, filename='oil_price_summary.txt'):
        """保存所有商品摘要内容到一个文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("商品市场摘要报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("数据来源: tradingeconomics.com\n")
                f.write("=" * 60 + "\n\n")

                commodity_names = {
                    'crude-oil': '原油',
                    'brent-crude-oil': '布伦特原油',
                    'gasoline': '汽油'
                }

                for summary_data in summaries_data:
                    commodity_type = summary_data.get('commodity_type', '')
                    commodity_name = commodity_names.get(commodity_type, commodity_type)

                    f.write(f"【{commodity_name}市场分析】\n")
                    f.write("-" * 40 + "\n")

                    if summary_data.get('title'):
                        f.write(f"标题: {summary_data['title']}\n\n")

                    if summary_data.get('main_content'):
                        f.write("市场动态:\n")
                        f.write(summary_data['main_content'])
                        f.write("\n\n")

                    f.write("=" * 60 + "\n\n")

            print(f"所有商品摘要内容已保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存摘要文件失败: {e}")
            return False
    
    def save_raw_commodities_to_file(self, commodities_data, filename='oil_commodities_data.json'):
        """保存原始商品数据到JSON文件"""
        try:
            # 只保存为JSON格式
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(commodities_data, f, ensure_ascii=False, indent=2)

            if commodities_data:
                print(f"原始商品数据已保存到: {filename}")
            else:
                print("警告: 没有找到商品数据")

            return True
        except Exception as e:
            print(f"保存原始商品数据文件失败: {e}")
            return False
    
    def clean_price_data(self, raw_data):
        """清理价格数据"""
        cleaned_data = []
        seen_commodities = set()

        for item in raw_data:
            commodity = item.get('commodity', '').strip()
            price = item.get('price', '').strip()

            # 只处理目标商品
            if not any(target in commodity for target in self.target_commodities):
                continue

            # 避免重复数据，优先选择有change字段的记录
            if commodity in seen_commodities:
                current_change = item.get('change', '').strip()
                # 如果当前项有change字段，替换之前的记录
                if current_change:
                    # 移除旧的记录
                    cleaned_data = [x for x in cleaned_data if x['commodity'] != commodity]
                else:
                    # 如果当前项没有change字段，跳过
                    continue

            # 清理价格数据
            clean_price = self.clean_price_value(price)
            if not clean_price:
                continue

            # 清理变化数据
            change = item.get('change', '').strip()  # change现在是"上升"或"下降"，直接保留
            change_price = self.clean_change_value(item.get('change_price', ''))

            # 提取日变化、月变化、年变化
            daily_change = self.clean_percent_value(item.get('daily_change', ''))
            monthly_change = self.clean_percent_value(item.get('monthly_change', ''))
            yearly_change = self.clean_percent_value(item.get('yearly_change', ''))

            cleaned_item = {
                'commodity': commodity,
                'price': clean_price,
                'price_unit': '美元/桶' if commodity in ['原油', '布伦特原油'] else '美元/加仑',
                'change': change,
                'change_price': change_price if change_price is not None else '',
                'daily_change': daily_change,
                'monthly_change': monthly_change,
                'yearly_change': yearly_change,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            cleaned_data.append(cleaned_item)
            seen_commodities.add(commodity)

        return cleaned_data

    def clean_price_value(self, price_str):
        """清理价格值"""
        if not price_str:
            return None

        # 提取数字
        price_match = re.search(r'(\d+\.?\d*)', str(price_str))
        if price_match:
            try:
                return float(price_match.group(1))
            except ValueError:
                return None
        return None

    def clean_change_value(self, change_str):
        """清理变化值"""
        if not change_str:
            return None

        # 提取数字（可能包含正负号）
        change_match = re.search(r'([+-]?\d+\.?\d*)', str(change_str))
        if change_match:
            try:
                return float(change_match.group(1))
            except ValueError:
                return None
        return None

    def clean_percent_value(self, percent_str):
        """清理百分比值"""
        if not percent_str:
            return None

        # 提取百分比数字
        percent_match = re.search(r'([+-]?\d+\.?\d*)%?', str(percent_str))
        if percent_match:
            try:
                value = float(percent_match.group(1))
                # 如果原字符串包含%，直接返回；否则可能需要转换
                if '%' in str(percent_str):
                    return f"{value}%"
                else:
                    return f"{value}%"
            except ValueError:
                return None
        return None

    def save_cleaned_data_to_csv(self, cleaned_data, filename='commodities_data.csv'):
        """保存清理后的数据到CSV文件"""
        if not cleaned_data:
            print("没有数据可保存")
            return False

        try:
            # 保存CSV格式
            with open(filename, 'w', encoding='utf-8-sig', newline='') as f:
                if cleaned_data:
                    fieldnames = cleaned_data[0].keys()
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(cleaned_data)

            print(f"清理后的数据已保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存清理后的数据失败: {e}")
            return False

    def save_cleaned_data_to_json(self, cleaned_data, filename='oil_commodities_data.json'):
        """保存清理后的数据到JSON文件（覆盖原始数据）"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, ensure_ascii=False, indent=2)

            print(f"清理后的数据已保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存清理后的JSON数据失败: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
    
    def run(self):
        """运行爬虫"""
        try:
            print("开始运行高级商品价格爬虫...")
            print("目标网站: 原油、布伦特原油、汽油")

            # 设置浏览器驱动
            if not self.setup_driver():
                return False

            all_summaries = []
            all_commodities_data = []

            # 遍历所有商品页面
            for commodity_type, url in self.urls.items():
                print(f"\n正在处理: {commodity_type}")

                # 获取页面内容
                html_content = self.get_page_content(url)
                if not html_content:
                    print(f"无法获取 {commodity_type} 页面内容")
                    continue

                print(f"{commodity_type} 页面内容获取成功，开始解析数据...")

                # 提取摘要内容
                summary_data = self.extract_summary(html_content, commodity_type)
                all_summaries.append(summary_data)

                # 提取商品数据
                commodities_data = self.extract_commodity_data(html_content)
                all_commodities_data.extend(commodities_data)

            # 数据处理和保存流程
            print("\n开始数据处理...")

            # 1. 保存摘要文件
            summary_saved = self.save_summaries_to_file(all_summaries)

            # 2. 清理商品数据
            cleaned_data = self.clean_price_data(all_commodities_data)

            # 3. 保存清理后的数据到CSV
            csv_saved = self.save_cleaned_data_to_csv(cleaned_data, 'commodities_data.csv')

            # 4. 保存清理后的数据到JSON（覆盖原始数据）
            json_saved = self.save_cleaned_data_to_json(cleaned_data, 'oil_commodities_data.json')

            # 打印结果摘要
            print("\n" + "="*60)
            print("爬取和处理结果摘要:")
            print(f"摘要内容保存: {'成功' if summary_saved else '失败'}")
            print(f"清理后CSV数据保存: {'成功' if csv_saved else '失败'}")
            print(f"清理后JSON数据保存: {'成功' if json_saved else '失败'}")
            print(f"原始数据条数: {len(all_commodities_data)}")
            print(f"清理后数据条数: {len(cleaned_data)}")
            print(f"处理的商品页面数: {len(all_summaries)}")

            if cleaned_data:
                print("\n最终清理后的商品数据:")
                for item in cleaned_data:
                    print(f"- {item['commodity']}: {item['price']} {item['price_unit']} (方向: {item['change']}, 变化: {item['change_price']})")

            print("\n生成的文件:")
            print("1. oil_price_summary.txt - 油价摘要")
            print("2. commodities_data.csv - 清理后的商品数据")
            print("3. oil_commodities_data.json - 清理后的商品数据(JSON格式)")

            return True

        except Exception as e:
            print(f"运行爬虫时出错: {e}")
            return False
        finally:
            self.cleanup()

if __name__ == "__main__":
    spider = AdvancedOilSpider()
    spider.run()
