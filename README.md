# 原油价格爬虫

这个项目用于爬取 tradingeconomics.com 网站的原油相关商品价格信息和市场分析。

## 功能特性

- 爬取三个商品页面的市场分析摘要（原油、布伦特原油、汽油）
- 爬取商品价格数据，包括涨跌方向、价格变化、各种变化百分比
- 自动数据清理和处理
- 输出三个文件：摘要、CSV数据、JSON数据

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

```bash
python advanced_oil_spider.py
```

## 输出文件

运行成功后会生成以下三个文件：

1. **oil_price_summary.txt** - 油价摘要
   - 原油市场分析
   - 布伦特原油市场分析
   - 汽油市场分析

2. **commodities_data.csv** - 清理后的商品数据（CSV格式）
   - 包含价格、涨跌方向、价格变化、各种变化百分比

3. **oil_commodities_data.json** - 清理后的商品数据（JSON格式）
   - 与CSV相同的数据，JSON格式

## 数据字段说明

- **commodity**: 商品名称（原油、布伦特原油、汽油）
- **price**: 当前价格
- **price_unit**: 价格单位（美元/桶 或 美元/加仑）
- **change**: 涨跌方向（"上升" 或 "下降"）
- **change_price**: 具体的价格变化值
- **daily_change**: 日变化百分比
- **monthly_change**: 月变化百分比
- **yearly_change**: 年变化百分比
- **update_time**: 数据更新时间

## 注意事项

1. **网络连接**：确保网络连接正常，能够访问目标网站
2. **Chrome浏览器**：需要Chrome浏览器（会自动下载ChromeDriver）
3. **数据准确性**：爬取的数据仅供参考，请以官方网站为准

## 许可证

本项目仅供学习和研究使用，请遵守目标网站的使用条款。
