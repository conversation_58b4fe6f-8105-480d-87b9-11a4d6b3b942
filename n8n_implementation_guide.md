# 在n8n中实现能源价格爬虫的指南

本指南将帮助您将现有的Python爬虫代码转换为n8n工作流。n8n是一个强大的工作流自动化平台，可以通过可视化界面创建自动化流程，无需编写大量代码。

## n8n简介

n8n是一个基于节点的工作流自动化工具，它允许您通过连接不同的节点来创建自动化工作流。每个节点代表一个特定的操作，如HTTP请求、数据处理、发送电子邮件等。

## 安装n8n

1. 确保您已安装Node.js (v14或更高版本)
2. 安装n8n:
   ```bash
   npm install n8n -g
   ```
3. 启动n8n:
   ```bash
   n8n start
   ```
4. 在浏览器中访问: `http://localhost:5678`

## 实现能源价格爬虫工作流

我们将创建两个独立的工作流：一个用于原油价格爬取，另一个用于天然气价格爬取。

### 原油价格爬虫工作流

#### 工作流概述

1. 使用HTTP Request节点获取网页内容
2. 使用Function节点解析HTML并提取数据
3. 使用Function节点处理和清理数据
4. 使用Write Binary File节点保存摘要文本
5. 使用Write JSON File节点保存JSON数据
6. 使用Function节点转换为CSV格式
7. 使用Write Binary File节点保存CSV数据

#### 详细步骤

##### 1. 创建新工作流

- 点击"Workflows"菜单
- 点击"+ New"按钮
- 命名为"Oil Price Spider"

##### 2. 添加开始触发器

- 添加"Schedule Trigger"节点
- 配置为每天运行一次（或根据需要设置）

##### 3. 添加HTTP Request节点获取原油页面

- 添加"HTTP Request"节点
- 配置如下:
  - 方法: GET
  - URL: https://zh.tradingeconomics.com/commodity/crude-oil
  - 认证: 无
  - 请求头: 添加User-Agent头
    ```
    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
    ```

##### 4. 添加Function节点解析原油页面HTML

- 添加"Function"节点
- 输入以下代码:

```javascript
const cheerio = require('cheerio');

// 获取HTTP请求的响应
const html = $input.item.json.data;
const $ = cheerio.load(html);

// 提取标题
let title = '';
const titleSelectors = ['h1', '.page-title', '.commodity-title'];
for (const selector of titleSelectors) {
  const titleElem = $(selector).first();
  if (titleElem.length) {
    title = titleElem.text().trim();
    break;
  }
}

// 提取市场分析内容
let mainContent = '';
let contentParagraphs = [];

// 查找div元素中的内容
$('div').each((i, elem) => {
  const text = $(elem).text().trim();
  if (text.length > 200 && 
      text.includes('WTI') && text.includes('期货') &&
      (text.includes('特朗普') || text.includes('俄罗斯') || text.includes('制裁') || 
       text.includes('欧盟') || text.includes('贸易协议') || text.includes('投资者') || 
       text.includes('谈判')) &&
      !text.includes('版权') && !text.includes('联系') && !text.includes('关于') && 
      !text.includes('隐私') && !text.includes('更多') && !text.includes('指标') && 
      !text.includes('国家') && !text.includes('Trading Economics')) {
    contentParagraphs.push(text);
  }
});

// 如果没找到，查找段落中的内容
if (contentParagraphs.length === 0) {
  $('p').each((i, elem) => {
    const text = $(elem).text().trim();
    if (text.length > 200 && 
        (text.includes('WTI') || text.includes('期货') || text.includes('特朗普') || 
         text.includes('俄罗斯') || text.includes('制裁') || text.includes('欧盟') || 
         text.includes('贸易协议') || text.includes('投资者') || text.includes('谈判')) &&
        !text.includes('版权') && !text.includes('联系') && !text.includes('关于') && 
        !text.includes('隐私') && !text.includes('更多') && !text.includes('指标') && 
        !text.includes('国家') && !text.includes('Trading Economics')) {
      contentParagraphs.push(text);
    }
  });
}

// 如果还没找到，查找包含基本市场关键词的内容
if (contentParagraphs.length === 0) {
  $('p, div').each((i, elem) => {
    const text = $(elem).text().trim();
    if (text.length > 100 && 
        (text.includes('上涨') || text.includes('下跌') || text.includes('美元') || 
         text.includes('桶') || text.includes('价格') || text.includes('市场') || 
         text.includes('供应') || text.includes('需求')) &&
        !text.includes('版权') && !text.includes('联系') && !text.includes('关于') && 
        !text.includes('隐私') && !text.includes('更多') && !text.includes('指标') && 
        !text.includes('国家')) {
      contentParagraphs.push(text);
    }
  });
}

// 取最长的内容作为主要内容
if (contentParagraphs.length > 0) {
  contentParagraphs.sort((a, b) => b.length - a.length);
  mainContent = contentParagraphs[0];
}

// 提取价格数据
let priceData = null;

// 查找包含"原油"的行
$('tr').each((i, elem) => {
  const text = $(elem).text().trim();
  if (text.includes('原油')) {
    const cells = $(elem).find('td');
    if (cells.length >= 6) {
      const price = $(cells[1]).text().trim();
      const changePrice = $(cells[3]).text().trim();
      
      // 判断涨跌方向
      let changeDirection = '';
      if (changePrice) {
        const changePriceNum = parseFloat(changePrice);
        if (changePriceNum > 0) {
          changeDirection = '上升';
        } else if (changePriceNum < 0) {
          changeDirection = '下降';
        }
      }
      
      priceData = {
        commodity: '原油',
        price: parseFloat(price.match(/(\d+\.?\d*)/)[0]),
        price_unit: '美元/桶',
        change: changeDirection,
        change_price: parseFloat(changePrice),
        daily_change: $(cells[4]).text().trim(),
        monthly_change: $(cells[5]).text().trim(),
        yearly_change: $(cells[6]).text().trim(),
        update_time: new Date().toISOString().replace('T', ' ').substring(0, 19)
      };
    }
    return false; // 找到后停止循环
  }
});

// 返回提取的数据
return {
  json: {
    commodity_type: 'crude-oil',
    title: title,
    main_content: mainContent,
    price_data: priceData,
    timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19)
  }
};
```

##### 5. 添加HTTP Request节点获取布伦特原油页面

- 添加另一个"HTTP Request"节点
- 配置与第一个类似，但URL改为:
  - URL: https://zh.tradingeconomics.com/commodity/brent-crude-oil

##### 6. 添加Function节点解析布伦特原油页面HTML

- 添加另一个"Function"节点
- 代码与第一个类似，但修改商品名称为"布伦特原油"，commodity_type为"brent-crude-oil"，price_unit为"美元/桶"

##### 7. 添加HTTP Request节点获取汽油页面

- 添加另一个"HTTP Request"节点
- 配置与前面类似，但URL改为:
  - URL: https://zh.tradingeconomics.com/commodity/gasoline

##### 8. 添加Function节点解析汽油页面HTML

- 添加另一个"Function"节点
- 代码与前面类似，但修改商品名称为"汽油"，commodity_type为"gasoline"，price_unit为"美元/加仑"

##### 9. 添加Merge节点合并所有数据

- 添加"Merge"节点
- 配置为合并所有前面的Function节点输出

##### 10. 添加Function节点生成摘要文本

- 添加"Function"节点
- 输入以下代码:

```javascript
// 获取合并的数据
const items = $input.all();
let summaryText = "商品市场摘要报告\n";
summaryText += "=" .repeat(60) + "\n";
summaryText += `生成时间: ${new Date().toISOString().replace('T', ' ').substring(0, 19)}\n`;
summaryText += "数据来源: tradingeconomics.com\n";
summaryText += "=" .repeat(60) + "\n\n";

// 商品类型映射
const commodityNames = {
  'crude-oil': '原油',
  'brent-crude-oil': '布伦特原油',
  'gasoline': '汽油'
};

// 为每个商品添加摘要
for (const item of items) {
  const data = item.json;
  const commodityType = data.commodity_type;
  const commodityName = commodityNames[commodityType] || commodityType;
  
  summaryText += `【${commodityName}市场分析】\n`;
  summaryText += "-" .repeat(40) + "\n";
  
  if (data.title) {
    summaryText += `标题: ${data.title}\n\n`;
  }
  
  if (data.main_content) {
    summaryText += "市场动态:\n";
    summaryText += data.main_content;
    summaryText += "\n\n";
  }
  
  summaryText += "=" .repeat(60) + "\n\n";
}

// 返回摘要文本
return {
  json: {
    summary_text: summaryText
  },
  binary: {
    data: {
      mimeType: 'text/plain',
      data: Buffer.from(summaryText).toString('base64')
    }
  }
};
```

##### 11. 添加Write Binary File节点保存摘要文本

- 添加"Write Binary File"节点
- 配置如下:
  - 属性名: data
  - 文件名: oil_price_summary.txt
  - 文件夹路径: 您的目标文件夹路径

##### 12. 添加Function节点整理价格数据

- 添加"Function"节点
- 输入以下代码:

```javascript
// 获取合并的数据
const items = $input.all();
let commoditiesData = [];

// 提取每个商品的价格数据
for (const item of items) {
  const data = item.json;
  if (data.price_data) {
    commoditiesData.push(data.price_data);
  }
}

// 返回整理后的数据
return {
  json: commoditiesData
};
```

##### 13. 添加Write JSON File节点保存JSON数据

- 添加"Write JSON File"节点
- 配置如下:
  - 文件名: oil_commodities_data.json
  - 文件夹路径: 您的目标文件夹路径
  - 漂亮打印: 是

##### 14. 添加Function节点转换为CSV格式

- 添加"Function"节点
- 输入以下代码:

```javascript
// 获取JSON数据
const data = $input.item.json;

// 如果没有数据，返回空
if (!data || data.length === 0) {
  return { json: { success: false, message: '没有数据可转换为CSV' } };
}

// 获取字段名
const fields = Object.keys(data[0]);

// 创建CSV头行
let csvContent = fields.join(',') + '\n';

// 添加数据行
for (const item of data) {
  const row = fields.map(field => {
    // 处理可能包含逗号的字段
    const value = item[field];
    if (typeof value === 'string' && value.includes(',')) {
      return `"${value}"`;
    }
    return value;
  }).join(',');
  csvContent += row + '\n';
}

// 返回CSV内容
return {
  json: { success: true },
  binary: {
    data: {
      mimeType: 'text/csv',
      data: Buffer.from(csvContent, 'utf-8').toString('base64')
    }
  }
};
```

##### 15. 添加Write Binary File节点保存CSV数据

- 添加"Write Binary File"节点
- 配置如下:
  - 属性名: data
  - 文件名: commodities_data.csv
  - 文件夹路径: 您的目标文件夹路径

### 天然气价格爬虫工作流

天然气价格爬虫工作流的结构与原油价格爬虫类似，只需要修改以下几点：

1. 工作流名称改为"Natural Gas Spider"
2. HTTP Request节点的URL改为:
   - https://zh.tradingeconomics.com/commodity/natural-gas
   - https://zh.tradingeconomics.com/commodity/uk-natural-gas
   - https://zh.tradingeconomics.com/commodity/lng
3. Function节点中的商品名称改为"天然气"、"英国天然气"和"LNG"
4. Function节点中的commodity_type改为"natural-gas"、"uk-natural-gas"和"lng"
5. Function节点中的price_unit根据商品类型设置为相应的单位
6. 输出文件名改为:
   - gas_price_summary.txt
   - gas_commodities_data.json
   - gas_commodities_data.csv

## 安装必要的n8n模块

为了使用cheerio解析HTML，您需要在n8n中安装这个模块：

1. 停止n8n服务
2. 安装cheerio:
   ```bash
   npm install cheerio --save
   ```
3. 重新启动n8n服务

## 运行工作流

1. 点击工作流右上角的"激活"按钮使工作流处于活动状态
2. 工作流将按照设定的计划自动运行
3. 您也可以点击"执行工作流"按钮手动运行工作流

## 优势与注意事项

### 使用n8n的优势

1. **可视化界面**：无需编写大量代码，通过拖放节点创建工作流
2. **易于维护**：工作流结构清晰，易于理解和修改
3. **自动化调度**：内置调度功能，可以按计划自动运行
4. **错误处理**：内置错误处理机制，可以设置失败通知
5. **扩展性**：可以轻松添加更多功能，如发送电子邮件通知、推送到数据库等

### 注意事项

1. **网站结构变化**：如果目标网站结构发生变化，需要相应地更新Function节点中的解析代码
2. **IP限制**：频繁请求可能导致IP被限制，考虑添加适当的延迟或使用代理
3. **数据验证**：添加数据验证逻辑，确保提取的数据有效
4. **错误通知**：配置错误通知，及时了解工作流运行状态

## 进阶功能

1. **添加数据库存储**：使用n8n的数据库节点将数据存储到数据库中
2. **添加通知功能**：当检测到价格变化超过阈值时发送通知
3. **添加数据可视化**：将数据发送到可视化平台，如Grafana或Power BI
4. **添加API接口**：使用n8n的Webhook节点创建API接口，允许其他系统查询数据

## 结论

通过n8n，您可以将原有的Python爬虫转换为可视化的工作流，更容易维护和扩展。虽然初始设置需要一些时间，但长期来看，这种方法可以大大减少维护成本，并提供更多的自动化和集成可能性。